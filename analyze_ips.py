#!/usr/bin/env python3
"""
<PERSON>ript to analyze IP addresses from rds_connections.txt and convert them to CIDR ranges.
This helps optimize the security group rules for RDS instances.
"""

import sys

def read_ips_from_file(filename):
    """Read IP addresses from the connections file."""
    ips = []
    try:
        with open(filename, 'r') as f:
            for line in f:
                line = line.strip()
                if line and ':' in line:
                    # Extract IP address (before the colon)
                    ip = line.split(':')[0]
                    # Basic IP validation
                    parts = ip.split('.')
                    if len(parts) == 4:
                        try:
                            all(0 <= int(part) <= 255 for part in parts)
                            ips.append(ip)
                        except ValueError:
                            print(f"Warning: Invalid IP address: {ip}")
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        sys.exit(1)

    return ips

def group_ips_by_network(ips):
    """Group IP addresses by their network prefixes."""
    networks = {}

    for ip in ips:
        # Group by /16 network (first two octets)
        parts = ip.split('.')
        network_16 = f"{parts[0]}.{parts[1]}.0.0/16"
        if network_16 not in networks:
            networks[network_16] = []
        networks[network_16].append(ip)

    return networks

def find_optimal_cidrs(ips):
    """Find optimal CIDR ranges that cover all IP addresses."""
    if not ips:
        return []

    # Group by /24 networks first
    networks_24 = {}
    for ip in ips:
        parts = ip.split('.')
        network = f"{parts[0]}.{parts[1]}.{parts[2]}.0/24"
        if network not in networks_24:
            networks_24[network] = []
        networks_24[network].append(ip)

    cidrs = []
    # For each /24 network, see if we can optimize
    for network, network_ips in networks_24.items():
        if len(network_ips) >= 128:  # More than half of /24
            cidrs.append(network)
        elif len(network_ips) >= 32:  # Quarter of /24, use /26
            parts = network.split('.')
            base = f"{parts[0]}.{parts[1]}.{parts[2]}"
            cidrs.append(f"{base}.0/26")
        else:
            # Use the /24 for simplicity
            cidrs.append(network)

    return cidrs

def analyze_aws_ranges(ips):
    """Analyze which AWS regions/services the IPs belong to."""
    aws_ranges = {}
    
    # Common AWS IP ranges (this is a simplified version)
    aws_prefixes = {
        '13.210.': 'ap-southeast-2 (Sydney)',
        '13.211.': 'ap-southeast-2 (Sydney)', 
        '13.236.': 'ap-southeast-2 (Sydney)',
        '13.238.': 'ap-southeast-2 (Sydney)',
        '13.239.': 'ap-southeast-2 (Sydney)',
        '13.54.': 'ap-southeast-2 (Sydney)',
        '13.55.': 'ap-southeast-2 (Sydney)',
        '16.176.': 'us-east-1 (N. Virginia)',
        '3.104.': 'ap-southeast-2 (Sydney)',
        '3.106.': 'ap-southeast-2 (Sydney)',
        '3.107.': 'ap-southeast-2 (Sydney)',
        '3.25.': 'ap-southeast-2 (Sydney)',
        '3.26.': 'ap-southeast-2 (Sydney)',
        '3.27.': 'ap-southeast-2 (Sydney)',
        '52.62.': 'ap-southeast-2 (Sydney)',
        '52.63.': 'ap-southeast-2 (Sydney)',
        '52.64.': 'ap-southeast-2 (Sydney)',
        '52.65.': 'ap-southeast-2 (Sydney)',
        '54.153.': 'us-west-1 (N. California)',
        '54.206.': 'ap-southeast-2 (Sydney)',
        '54.252.': 'ap-southeast-2 (Sydney)',
        '54.253.': 'ap-southeast-2 (Sydney)',
        '54.66.': 'ap-southeast-2 (Sydney)',
        '54.79.': 'ap-southeast-2 (Sydney)',
    }
    
    for ip in ips:
        found = False
        for prefix, region in aws_prefixes.items():
            if ip.startswith(prefix):
                if region not in aws_ranges:
                    aws_ranges[region] = []
                aws_ranges[region].append(ip)
                found = True
                break
        if not found:
            if 'Unknown/Other' not in aws_ranges:
                aws_ranges['Unknown/Other'] = []
            aws_ranges['Unknown/Other'].append(ip)
    
    return aws_ranges

def main():
    filename = 'rds_connections.txt'
    
    print("🔍 Analyzing RDS connection IPs for SOC2 compliance...")
    print("=" * 60)
    
    # Read IPs from file
    ips = read_ips_from_file(filename)
    print(f"📊 Total unique IPs found: {len(set(ips))}")
    
    # Analyze AWS regions
    aws_ranges = analyze_aws_ranges(ips)
    print(f"\n🌍 AWS Regions identified:")
    for region, region_ips in aws_ranges.items():
        print(f"  • {region}: {len(region_ips)} IPs")
    
    # Group by networks
    networks = group_ips_by_network(ips)
    print(f"\n🌐 Network distribution (/16 networks): {len(networks)}")
    
    # Show top networks
    sorted_networks = sorted(networks.items(), key=lambda x: len(x[1]), reverse=True)
    print("\n📈 Top 10 networks by IP count:")
    for network, network_ips in sorted_networks[:10]:
        print(f"  • {network}: {len(network_ips)} IPs")
    
    # Generate optimized CIDR ranges
    print(f"\n🎯 Generating optimized CIDR ranges...")
    
    # Group by major AWS regions for better optimization
    region_cidrs = {}
    for region, region_ips in aws_ranges.items():
        if region != 'Unknown/Other' and region_ips:
            unique_ips = list(set(region_ips))
            cidrs = find_optimal_cidrs(unique_ips)
            region_cidrs[region] = cidrs
    
    print(f"\n📋 Recommended CIDR ranges by region:")
    total_cidrs = 0
    for region, cidrs in region_cidrs.items():
        print(f"\n  {region}:")
        for cidr in sorted(cidrs):
            print(f"    • {cidr}")
        total_cidrs += len(cidrs)
    
    print(f"\n✅ Summary:")
    print(f"  • Original IPs: {len(set(ips))}")
    print(f"  • Optimized to: {total_cidrs} CIDR ranges")
    print(f"  • Reduction: {len(set(ips)) - total_cidrs} fewer rules")
    
    # Generate Terraform security group rules
    print(f"\n🔧 Terraform security group ingress rules:")
    print("```hcl")
    for region, cidrs in region_cidrs.items():
        if cidrs:
            print(f"  # {region}")
            for cidr in sorted(cidrs):
                print(f"  ingress {{")
                print(f"    from_port   = 5432")
                print(f"    to_port     = 5432") 
                print(f"    protocol    = \"tcp\"")
                print(f"    cidr_blocks = [\"{cidr}\"]")
                print(f"    description = \"PostgreSQL access from {region}\"")
                print(f"  }}")
                print()
    print("```")

if __name__ == "__main__":
    main()
