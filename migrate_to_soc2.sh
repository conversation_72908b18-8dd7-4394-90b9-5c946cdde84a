#!/bin/bash

# SOC2 Compliance Migration Script for RDS
# This script helps migrate existing RDS configurations to SOC2 compliant settings

set -e

echo "🔒 SOC2 Compliance Migration for RDS Instances"
echo "=============================================="
echo ""

# Function to check if a file exists
check_file() {
    if [ ! -f "$1" ]; then
        echo "❌ Error: File $1 not found"
        exit 1
    fi
}

# Function to backup a file
backup_file() {
    local file="$1"
    local backup="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$file" "$backup"
    echo "📁 Backed up $file to $backup"
}

# Function to update main.tf files
update_main_tf() {
    local file="$1"
    echo "🔧 Updating $file for SOC2 compliance..."
    
    # Check if the file needs updating
    if grep -q "rds_publicly_accessible.*=" "$file" 2>/dev/null; then
        echo "   ✅ File already has rds_publicly_accessible setting"
    else
        echo "   ➕ Adding rds_publicly_accessible = false"
        # Add the setting after rds_apply_changes_immediately if it exists
        if grep -q "rds_apply_changes_immediately" "$file"; then
            sed -i '/rds_apply_changes_immediately/a\  rds_publicly_accessible    = false' "$file"
        else
            echo "   ⚠️  Please manually add: rds_publicly_accessible = false"
        fi
    fi
    
    if grep -q "enable_soc2_compliance.*=" "$file" 2>/dev/null; then
        echo "   ✅ File already has enable_soc2_compliance setting"
    else
        echo "   ➕ Adding enable_soc2_compliance = true"
        # Add the setting after rds_publicly_accessible if it exists
        if grep -q "rds_publicly_accessible" "$file"; then
            sed -i '/rds_publicly_accessible/a\  enable_soc2_compliance     = true' "$file"
        else
            echo "   ⚠️  Please manually add: enable_soc2_compliance = true"
        fi
    fi
}

# Main migration logic
main() {
    echo "🔍 Scanning for RDS configurations..."
    
    # Find all main.tf files that use eyecue_rds module
    rds_files=$(find . -name "main.tf" -exec grep -l "module.*eyecue_rds" {} \; 2>/dev/null || true)
    
    if [ -z "$rds_files" ]; then
        echo "❌ No RDS configurations found"
        exit 1
    fi
    
    echo "📋 Found RDS configurations:"
    echo "$rds_files" | while read -r file; do
        echo "   • $file"
    done
    echo ""
    
    # Ask for confirmation
    read -p "🤔 Do you want to proceed with SOC2 migration? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Migration cancelled"
        exit 1
    fi
    
    echo ""
    echo "🚀 Starting migration..."
    echo ""
    
    # Process each file
    echo "$rds_files" | while read -r file; do
        echo "📝 Processing $file..."
        backup_file "$file"
        update_main_tf "$file"
        echo "   ✅ Updated $file"
        echo ""
    done
    
    echo "✅ Migration completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Review the changes in each file"
    echo "2. Test in development environment first"
    echo "3. Run 'terraform plan' to see the changes"
    echo "4. Apply changes during maintenance window"
    echo ""
    echo "📚 For more information, see SOC2_RDS_COMPLIANCE.md"
}

# Check if we're in the right directory
if [ ! -d "modules/eyecue_rds" ]; then
    echo "❌ Error: Please run this script from the terraform root directory"
    echo "   Expected to find: modules/eyecue_rds/"
    exit 1
fi

# Run the migration
main

echo ""
echo "🔒 SOC2 Compliance Migration Complete!"
echo "   Remember to test thoroughly before applying to production."
