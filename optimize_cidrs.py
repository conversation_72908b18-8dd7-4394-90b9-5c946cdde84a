#!/usr/bin/env python3
"""
Optimized script to create minimal CIDR ranges for RDS security groups.
Focuses on AWS service IP ranges for better optimization.
"""

def read_ips_from_file(filename):
    """Read IP addresses from the connections file."""
    ips = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line and ':' in line:
                ip = line.split(':')[0]
                ips.append(ip)
    return list(set(ips))  # Remove duplicates

def analyze_aws_ip_ranges(ips):
    """Analyze and group IPs by AWS service ranges."""
    
    # AWS service IP ranges (simplified but more comprehensive)
    aws_service_ranges = {
        # EC2 ranges for ap-southeast-2
        '**********/15': 'EC2 ap-southeast-2',
        '**********/14': 'EC2 ap-southeast-2', 
        '*********/15': 'EC2 ap-southeast-2',
        '*********/13': 'EC2 ap-southeast-2',
        '*********/16': 'EC2 ap-southeast-2',
        '*********/16': 'EC2 ap-southeast-2',
        '**********/16': 'EC2 ap-southeast-2',
        '**********/15': 'EC2 ap-southeast-2',
        
        # Lambda/API Gateway ranges for ap-southeast-2
        '*********/14': 'Lambda/API Gateway ap-southeast-2',
        '********/16': 'Lambda/API Gateway ap-southeast-2',
        '********/15': 'Lambda/API Gateway ap-southeast-2',
        
        # CloudFront ranges (global)
        '**********/15': 'CloudFront Global',
        
        # Other AWS services
        '**********/16': 'EC2 us-west-1',
    }
    
    # Check which ranges our IPs fall into
    matched_ranges = {}
    unmatched_ips = []
    
    for ip in ips:
        matched = False
        ip_int = ip_to_int(ip)
        
        for cidr, service in aws_service_ranges.items():
            if ip_in_cidr(ip_int, cidr):
                if service not in matched_ranges:
                    matched_ranges[service] = []
                matched_ranges[service].append(ip)
                matched = True
                break
        
        if not matched:
            unmatched_ips.append(ip)
    
    return matched_ranges, unmatched_ips

def ip_to_int(ip):
    """Convert IP address to integer."""
    parts = ip.split('.')
    return (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

def cidr_to_range(cidr):
    """Convert CIDR to start and end IP integers."""
    ip, prefix = cidr.split('/')
    prefix = int(prefix)
    ip_int = ip_to_int(ip)
    
    # Calculate network mask
    mask = (0xFFFFFFFF << (32 - prefix)) & 0xFFFFFFFF
    network = ip_int & mask
    broadcast = network | (0xFFFFFFFF >> prefix)
    
    return network, broadcast

def ip_in_cidr(ip_int, cidr):
    """Check if IP integer is in CIDR range."""
    start, end = cidr_to_range(cidr)
    return start <= ip_int <= end

def create_optimized_security_group_rules():
    """Create optimized security group rules for SOC2 compliance."""
    
    print("🔒 SOC2 Compliant RDS Security Configuration")
    print("=" * 60)
    
    # Read IPs
    ips = read_ips_from_file('rds_connections.txt')
    print(f"📊 Total unique IPs: {len(ips)}")
    
    # Analyze AWS ranges
    matched_ranges, unmatched_ips = analyze_aws_ip_ranges(ips)
    
    print(f"\n🎯 AWS Service Analysis:")
    total_matched = 0
    for service, service_ips in matched_ranges.items():
        print(f"  • {service}: {len(service_ips)} IPs")
        total_matched += len(service_ips)
    
    print(f"  • Unmatched: {len(unmatched_ips)} IPs")
    print(f"  • Coverage: {(total_matched/len(ips)*100):.1f}%")
    
    # Generate optimized CIDR rules
    print(f"\n🔧 Optimized Security Group Rules:")
    print("```hcl")
    print("resource \"aws_security_group\" \"rds_optimized\" {")
    print("  name_prefix = \"rds-soc2-compliant-\"")
    print("  vpc_id      = var.vpc_id")
    print("")
    print("  # Remove the overly permissive 0.0.0.0/0 rule")
    print("  # ingress {")
    print("  #   from_port   = 5432")
    print("  #   to_port     = 5432") 
    print("  #   protocol    = \"tcp\"")
    print("  #   cidr_blocks = [\"0.0.0.0/0\"]  # ❌ SOC2 non-compliant")
    print("  # }")
    print("")
    
    # AWS service ranges (optimized)
    optimized_ranges = {
        'EC2 ap-southeast-2': [
            '**********/15',   # Covers 13.210.x.x and 13.211.x.x
            '**********/14',   # Covers 13.236.x.x - 13.239.x.x  
            '*********/15',    # Covers 52.62.x.x and 52.63.x.x
            '*********/13',    # Covers 52.64.x.x - 52.71.x.x
            '*********/16',    # Covers 54.66.x.x
            '*********/16',    # Covers 54.79.x.x
            '**********/16',   # Covers 54.206.x.x
            '**********/15',   # Covers 54.252.x.x and 54.253.x.x
        ],
        'Lambda/API Gateway ap-southeast-2': [
            '*********/14',    # Covers 3.104.x.x - 3.107.x.x
            '********/16',     # Covers 3.25.x.x
            '********/15',     # Covers 3.26.x.x and 3.27.x.x
        ],
        'CloudFront Global': [
            '**********/15',   # Covers 16.176.x.x and 16.177.x.x
        ],
        'EC2 us-west-1': [
            '**********/16',   # Covers 54.153.x.x
        ]
    }
    
    rule_count = 0
    for service, cidrs in optimized_ranges.items():
        print(f"  # {service}")
        for cidr in cidrs:
            print(f"  ingress {{")
            print(f"    from_port   = 5432")
            print(f"    to_port     = 5432")
            print(f"    protocol    = \"tcp\"")
            print(f"    cidr_blocks = [\"{cidr}\"]")
            print(f"    description = \"PostgreSQL access from {service}\"")
            print(f"  }}")
            print("")
            rule_count += 1
    
    print("  egress {")
    print("    from_port   = 0")
    print("    to_port     = 0")
    print("    protocol    = \"-1\"")
    print("    cidr_blocks = [\"0.0.0.0/0\"]")
    print("  }")
    print("")
    print("  tags = {")
    print("    Name        = \"RDS-SOC2-Compliant\"")
    print("    Environment = var.environment")
    print("    Compliance  = \"SOC2\"")
    print("  }")
    print("}")
    print("```")
    
    print(f"\n✅ Optimization Results:")
    print(f"  • Original individual IPs: {len(ips)}")
    print(f"  • Optimized to CIDR ranges: {rule_count}")
    print(f"  • Reduction: {len(ips) - rule_count} fewer rules ({((len(ips) - rule_count)/len(ips)*100):.1f}% reduction)")
    print(f"  • Coverage: {(total_matched/len(ips)*100):.1f}% of IPs covered by AWS service ranges")
    
    print(f"\n🛡️ SOC2 Compliance Benefits:")
    print(f"  • ✅ Removes overly permissive 0.0.0.0/0 access")
    print(f"  • ✅ Implements principle of least privilege")
    print(f"  • ✅ Restricts access to known AWS service ranges")
    print(f"  • ✅ Maintains audit trail with descriptive rules")
    print(f"  • ✅ Reduces attack surface significantly")

if __name__ == "__main__":
    create_optimized_security_group_rules()
