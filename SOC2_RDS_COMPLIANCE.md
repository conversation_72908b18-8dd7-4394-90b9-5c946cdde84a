# SOC2 Compliance for RDS Instances

## Overview

This document outlines the changes made to ensure RDS instances (eyecue and master-postgres) are SOC2 compliant by implementing the principle of least privilege and removing overly permissive network access.

## Issues Identified

### 1. Publicly Accessible RDS Instances
- **Issue**: Both RDS instances had `publicly_accessible = true`
- **Risk**: Exposes databases to the public internet
- **SOC2 Requirement**: CC6.1 - Logical and physical access controls

### 2. Overly Permissive Security Groups
- **Issue**: Security groups allowed access from `0.0.0.0/0` (anywhere on the internet)
- **Risk**: Violates principle of least privilege
- **SOC2 Requirement**: CC6.3 - Network access controls

## Solution Implemented

### 1. Configurable Public Access
Added `rds_publicly_accessible` variable:
```hcl
variable "rds_publicly_accessible" {
  default     = false
  type        = bool
  description = "Whether the RDS instance is publicly accessible. Set to false for SOC2 compliance."
}
```

### 2. SOC2 Compliant Security Groups
Replaced `0.0.0.0/0` access with specific AWS service CIDR ranges:

#### Optimized CIDR Ranges
- **Original**: 1,962 individual IP addresses
- **Optimized**: 13 CIDR ranges (99.3% reduction)
- **Coverage**: 95.7% of original IPs

#### AWS Service Ranges Included:
- **EC2 ap-southeast-2**: `**********/15`, `**********/14`, `*********/15`, `*********/13`, `*********/16`, `*********/16`, `**********/16`, `**********/15`
- **Lambda/API Gateway ap-southeast-2**: `*********/14`, `********/16`, `********/15`
- **CloudFront Global**: `**********/15`
- **EC2 us-west-1**: `**********/16`

### 3. Backward Compatibility
Added `enable_soc2_compliance` variable for gradual migration:
```hcl
variable "enable_soc2_compliance" {
  default     = true
  type        = bool
  description = "Enable SOC2 compliant security group rules. Set to false to use legacy 0.0.0.0/0 access."
}
```

## Implementation Steps

### Step 1: Update Module Variables
The following variables have been added to `modules/eyecue_rds/vars.tf`:
- `rds_publicly_accessible` (default: `false`)
- `enable_soc2_compliance` (default: `true`)

### Step 2: Update Existing Deployments
For existing deployments, you can choose your migration approach:

#### Option A: Immediate SOC2 Compliance (Recommended)
```hcl
module "eyecue_rds" {
  source = "../../../../modules/eyecue_rds"
  
  # SOC2 Compliant settings (default)
  rds_publicly_accessible = false
  enable_soc2_compliance  = true
  
  # ... other variables
}
```

#### Option B: Gradual Migration
```hcl
module "eyecue_rds" {
  source = "../../../../modules/eyecue_rds"
  
  # Keep current behavior temporarily
  rds_publicly_accessible = true   # Current setting
  enable_soc2_compliance  = false  # Use legacy rules
  
  # ... other variables
}
```

### Step 3: Verify Connectivity
After applying SOC2 compliant settings:
1. Test application connectivity to RDS
2. Monitor CloudWatch logs for connection issues
3. Verify all legitimate traffic is still allowed

## SOC2 Compliance Benefits

✅ **CC6.1 - Logical Access Controls**
- RDS instances are no longer publicly accessible by default
- Access is restricted to specific AWS service ranges

✅ **CC6.3 - Network Access Controls**
- Implements principle of least privilege
- Removes overly permissive `0.0.0.0/0` rules
- Maintains detailed audit trail with descriptive security group rules

✅ **CC6.6 - Logical Access Control Monitoring**
- Security group changes are logged in CloudTrail
- Clear rule descriptions for audit purposes

## Monitoring and Maintenance

### CloudWatch Metrics to Monitor
- `DatabaseConnections` - Monitor for connection drops
- `DatabaseConnectionErrors` - Watch for authentication failures

### Regular Maintenance
1. **Quarterly Review**: Review AWS IP ranges for updates
2. **Access Audit**: Verify only necessary services have access
3. **Compliance Check**: Ensure no `0.0.0.0/0` rules are added

## Troubleshooting

### Connection Issues After Migration
1. Check if source IP is covered by allowed CIDR ranges
2. Verify security group rules are applied correctly
3. Confirm RDS subnet group configuration

### Adding New IP Ranges
If legitimate traffic is blocked:
1. Identify the source AWS service
2. Find the appropriate AWS IP range
3. Add specific CIDR block (avoid `0.0.0.0/0`)

## Files Modified

- `modules/eyecue_rds/main.tf` - Updated security group rules and RDS configuration
- `modules/eyecue_rds/vars.tf` - Added new variables for SOC2 compliance
- `rds_connections.txt` - Source data for IP analysis
- `analyze_ips.py` - Script to analyze connection patterns
- `optimize_cidrs.py` - Script to generate optimized CIDR ranges

## Next Steps

1. **Test in Development**: Apply changes to dev environment first
2. **Validate Connectivity**: Ensure all applications can still connect
3. **Apply to Production**: Roll out during maintenance window
4. **Monitor**: Watch for any connection issues post-deployment
5. **Document**: Update runbooks with new security group configuration
